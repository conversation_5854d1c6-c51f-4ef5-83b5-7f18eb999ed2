-- Add comprehensive audit logging for project-related tables
-- This migration creates audit tables and triggers for complete change tracking
-- ============================================================================
-- AUDIT TABLE CREATION
-- ============================================================================
-- Project Stage Audit Table
CREATE TABLE "public"."project_stage_audit" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns
	project_stage_id BIGINT,
	project_id UUID,
	name TEXT,
	description TEXT,
	stage_order INTEGER,
	stage INTEGER,
	gateway_qualitative_scorecard JSONB,
	date_started TIMESTAMPTZ,
	date_completed TIMESTAMPTZ,
	completion_notes TEXT,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."project_stage_audit" IS 'Audit log of all changes to project stages';

-- Gateway Checklist Item Audit Table
CREATE TABLE "public"."gateway_checklist_item_audit" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns
	gateway_checklist_item_id BIGINT,
	project_stage_id BIGINT,
	name TEXT,
	description TEXT,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."gateway_checklist_item_audit" IS 'Audit log of all changes to gateway checklist items';

-- Project Gateway Stage Info Audit Table
CREATE TABLE "public"."project_gateway_stage_info_audit" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns
	project_gateway_stage_info_id BIGINT,
	project_stage_id BIGINT,
	basement_floors NUMERIC(15, 2),
	ground_floor NUMERIC(15, 2),
	upper_floors NUMERIC(15, 2),
	total_gross_internal_floor_area NUMERIC(15, 2),
	usable_area NUMERIC(15, 2),
	circulation_area NUMERIC(15, 2),
	ancillary_areas NUMERIC(15, 2),
	internal_divisions NUMERIC(15, 2),
	spaces_not_enclosed NUMERIC(15, 2),
	total_gross_internal_floor_area_2 NUMERIC(15, 2),
	internal_cube NUMERIC(15, 2),
	area_of_lowest_floor NUMERIC(15, 2),
	site_area NUMERIC(15, 2),
	number_of_units NUMERIC(15, 2),
	nr_of_storeys NUMERIC(15, 2),
	nr_of_storeys_primary NUMERIC(15, 2),
	nr_of_storeys_secondary NUMERIC(15, 2),
	basement_storeys_included_above NUMERIC(15, 2),
	average_storey_height NUMERIC(15, 2),
	below_ground_floors NUMERIC(15, 2),
	ground_floor_height NUMERIC(15, 2),
	above_ground_floors NUMERIC(15, 2),
	external_vertical_envelope NUMERIC(15, 2),
	additional_data JSONB,
	created_by_user_id UUID,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."project_gateway_stage_info_audit" IS 'Audit log of all changes to project gateway stage information';

-- Risk Register Audit Table
CREATE TABLE "public"."risk_register_audit" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns
	risk_id INTEGER,
	project_id UUID,
	title TEXT,
	description TEXT,
	status TEXT,
	wbs_library_item_id UUID,
	date_identified DATE,
	cause TEXT,
	effect TEXT,
	program_impact TEXT,
	probability NUMERIC(5, 2),
	potential_impact NUMERIC(15, 2),
	mitigation_plan TEXT,
	date_for_review DATE,
	risk_owner_user_id UUID,
	risk_owner_name TEXT,
	risk_owner_email TEXT,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."risk_register_audit" IS 'Audit log of all changes to risk register entries';

-- Approved Changes Audit Table
CREATE TABLE "public"."approved_changes_audit" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns
	approved_change_id INTEGER,
	project_id UUID,
	title TEXT,
	description TEXT,
	status TEXT,
	wbs_library_item_id UUID,
	date_identified DATE,
	date_approved DATE,
	cause TEXT,
	effect TEXT,
	program_impact TEXT,
	potential_impact NUMERIC(15, 2),
	mitigation_plan TEXT,
	date_for_review DATE,
	risk_owner_user_id UUID,
	risk_owner_name TEXT,
	risk_owner_email TEXT,
	approved_by_user_id UUID,
	original_risk_id INTEGER,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."approved_changes_audit" IS 'Audit log of all changes to approved changes entries';

-- ============================================================================
-- AUDIT TRIGGER FUNCTIONS
-- ============================================================================
-- Generic audit trigger function for project_stage
CREATE OR REPLACE FUNCTION public.audit_project_stage_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.project_stage_id, OLD.project_id, OLD.name, OLD.description, OLD.stage_order, OLD.stage,
            OLD.gateway_qualitative_scorecard, OLD.date_started, OLD.date_completed, OLD.completion_notes,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Generic audit trigger function for gateway_checklist_item
CREATE OR REPLACE FUNCTION public.audit_gateway_checklist_item_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.gateway_checklist_item_id, OLD.project_stage_id, OLD.name, OLD.description,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Generic audit trigger function for project_gateway_stage_info
CREATE OR REPLACE FUNCTION public.audit_project_gateway_stage_info_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.project_gateway_stage_info_id, OLD.project_stage_id, OLD.basement_floors, OLD.ground_floor, OLD.upper_floors,
            OLD.total_gross_internal_floor_area, OLD.usable_area, OLD.circulation_area, OLD.ancillary_areas, OLD.internal_divisions,
            OLD.spaces_not_enclosed, OLD.total_gross_internal_floor_area_2, OLD.internal_cube, OLD.area_of_lowest_floor,
            OLD.site_area, OLD.number_of_units, OLD.nr_of_storeys, OLD.nr_of_storeys_primary, OLD.nr_of_storeys_secondary,
            OLD.basement_storeys_included_above, OLD.average_storey_height, OLD.below_ground_floors, OLD.ground_floor_height,
            OLD.above_ground_floors, OLD.external_vertical_envelope, OLD.additional_data, OLD.created_by_user_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Generic audit trigger function for risk_register
CREATE OR REPLACE FUNCTION public.audit_risk_register_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.probability, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Generic audit trigger function for approved_changes
CREATE OR REPLACE FUNCTION public.audit_approved_changes_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.approved_change_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.date_approved, OLD.cause, OLD.effect, OLD.program_impact, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.approved_by_user_id, OLD.original_risk_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CREATE TRIGGERS
-- ============================================================================
-- Project Stage Audit Trigger
CREATE TRIGGER audit_project_stage_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.project_stage FOR EACH ROW
EXECUTE FUNCTION public.audit_project_stage_changes ();

-- Gateway Checklist Item Audit Trigger
CREATE TRIGGER audit_gateway_checklist_item_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.gateway_checklist_item FOR EACH ROW
EXECUTE FUNCTION public.audit_gateway_checklist_item_changes ();

-- Project Gateway Stage Info Audit Trigger
CREATE TRIGGER audit_project_gateway_stage_info_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.project_gateway_stage_info FOR EACH ROW
EXECUTE FUNCTION public.audit_project_gateway_stage_info_changes ();

-- Risk Register Audit Trigger
CREATE TRIGGER audit_risk_register_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.risk_register FOR EACH ROW
EXECUTE FUNCTION public.audit_risk_register_changes ();

-- Approved Changes Audit Trigger
CREATE TRIGGER audit_approved_changes_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.approved_changes FOR EACH ROW
EXECUTE FUNCTION public.audit_approved_changes_changes ();

-- Budget Line Item Audit Table (Modern Pattern)
CREATE TABLE "public"."budget_line_item_audit_new" (
	audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
	changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	old_values JSONB,
	new_values JSONB,
	-- Original table columns for easier querying
	budget_line_item_id BIGINT,
	project_id UUID,
	wbs_library_item_id UUID,
	quantity NUMERIC(15, 2),
	unit TEXT,
	material_rate NUMERIC(15, 2),
	labor_rate NUMERIC(15, 2),
	productivity_per_hour NUMERIC(15, 2),
	unit_rate_manual_override BOOLEAN,
	unit_rate NUMERIC(15, 2),
	factor NUMERIC(15, 2),
	remarks TEXT,
	cost_certainty NUMERIC(5, 2),
	design_certainty NUMERIC(5, 2),
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
);

COMMENT ON TABLE "public"."budget_line_item_audit_new" IS 'Modern audit log of all changes to budget line items';

-- Create a system user profile for non-authenticated operations
INSERT INTO
	public.profile (user_id, email, full_name)
VALUES
	(
		'00000000-0000-0000-0000-000000000000',
		'system@internal',
		'System User'
	)
ON CONFLICT (user_id) DO NOTHING;

-- Generic audit trigger function for budget_line_item_current
CREATE OR REPLACE FUNCTION public.audit_budget_line_item_changes () RETURNS TRIGGER
SET
	search_path = '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit_new (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit_new (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit_new (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Budget Line Item Audit Trigger (Modern Pattern)
CREATE TRIGGER audit_budget_line_item_trigger
AFTER INSERT
OR
UPDATE
OR DELETE ON public.budget_line_item_current FOR EACH ROW
EXECUTE FUNCTION public.audit_budget_line_item_changes ();

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================
-- Project Stage Audit Indexes
CREATE INDEX idx_project_stage_audit_project_id ON public.project_stage_audit (project_id);

CREATE INDEX idx_project_stage_audit_changed_by ON public.project_stage_audit (changed_by);

CREATE INDEX idx_project_stage_audit_changed_at ON public.project_stage_audit (changed_at);

CREATE INDEX idx_project_stage_audit_operation_type ON public.project_stage_audit (operation_type);

-- Budget Line Item Audit Indexes
CREATE INDEX idx_budget_line_item_audit_new_project_id ON public.budget_line_item_audit_new (project_id);

CREATE INDEX idx_budget_line_item_audit_new_changed_by ON public.budget_line_item_audit_new (changed_by);

CREATE INDEX idx_budget_line_item_audit_new_changed_at ON public.budget_line_item_audit_new (changed_at);

CREATE INDEX idx_budget_line_item_audit_new_operation_type ON public.budget_line_item_audit_new (operation_type);

-- Gateway Checklist Item Audit Indexes
CREATE INDEX idx_gateway_checklist_item_audit_project_stage_id ON public.gateway_checklist_item_audit (project_stage_id);

CREATE INDEX idx_gateway_checklist_item_audit_changed_by ON public.gateway_checklist_item_audit (changed_by);

CREATE INDEX idx_gateway_checklist_item_audit_changed_at ON public.gateway_checklist_item_audit (changed_at);

-- Project Gateway Stage Info Audit Indexes
CREATE INDEX idx_project_gateway_stage_info_audit_project_stage_id ON public.project_gateway_stage_info_audit (project_stage_id);

CREATE INDEX idx_project_gateway_stage_info_audit_changed_by ON public.project_gateway_stage_info_audit (changed_by);

CREATE INDEX idx_project_gateway_stage_info_audit_changed_at ON public.project_gateway_stage_info_audit (changed_at);

-- Risk Register Audit Indexes
CREATE INDEX idx_risk_register_audit_project_id ON public.risk_register_audit (project_id);

CREATE INDEX idx_risk_register_audit_changed_by ON public.risk_register_audit (changed_by);

CREATE INDEX idx_risk_register_audit_changed_at ON public.risk_register_audit (changed_at);

CREATE INDEX idx_risk_register_audit_risk_id ON public.risk_register_audit (risk_id);

-- Approved Changes Audit Indexes
CREATE INDEX idx_approved_changes_audit_project_id ON public.approved_changes_audit (project_id);

CREATE INDEX idx_approved_changes_audit_changed_by ON public.approved_changes_audit (changed_by);

CREATE INDEX idx_approved_changes_audit_changed_at ON public.approved_changes_audit (changed_at);

CREATE INDEX idx_approved_changes_audit_approved_change_id ON public.approved_changes_audit (approved_change_id);

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================
-- Enable RLS on all audit tables
ALTER TABLE public.project_stage_audit ENABLE ROW LEVEL SECURITY;

ALTER TABLE public.gateway_checklist_item_audit ENABLE ROW LEVEL SECURITY;

ALTER TABLE public.project_gateway_stage_info_audit ENABLE ROW LEVEL SECURITY;

ALTER TABLE public.risk_register_audit ENABLE ROW LEVEL SECURITY;

ALTER TABLE public.approved_changes_audit ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- GRANT PERMISSIONS TO SERVICE ROLE
-- ============================================================================
-- Grant access to service_role for all audit tables
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.project_stage_audit TO service_role;

GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.gateway_checklist_item_audit TO service_role;

GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.project_gateway_stage_info_audit TO service_role;

GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.risk_register_audit TO service_role;

GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.approved_changes_audit TO service_role;

-- ============================================================================
-- CREATE RLS POLICIES
-- ============================================================================
-- Project Stage Audit Policies
-- Users can view audit records for projects they have access to
CREATE POLICY "Users can view project stage audit for accessible projects" ON public.project_stage_audit FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert project stage audit records" ON public.project_stage_audit FOR INSERT TO authenticated
WITH
	CHECK (true);

-- Gateway Checklist Item Audit Policies
-- Users can view audit records for project stages they have access to
CREATE POLICY "Users can view gateway checklist item audit for accessible projects" ON public.gateway_checklist_item_audit FOR
SELECT
	TO authenticated USING (
		EXISTS (
			SELECT
				1
			FROM
				public.project_stage ps
			WHERE
				ps.project_stage_id = gateway_checklist_item_audit.project_stage_id
				AND public.can_access_project (ps.project_id)
		)
	);

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert gateway checklist item audit records" ON public.gateway_checklist_item_audit FOR INSERT TO authenticated
WITH
	CHECK (true);

-- Project Gateway Stage Info Audit Policies
-- Users can view audit records for project stages they have access to
CREATE POLICY "Users can view project gateway stage info audit for accessible projects" ON public.project_gateway_stage_info_audit FOR
SELECT
	TO authenticated USING (
		EXISTS (
			SELECT
				1
			FROM
				public.project_stage ps
			WHERE
				ps.project_stage_id = project_gateway_stage_info_audit.project_stage_id
				AND public.can_access_project (ps.project_id)
		)
	);

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert project gateway stage info audit records" ON public.project_gateway_stage_info_audit FOR INSERT TO authenticated
WITH
	CHECK (true);

-- Risk Register Audit Policies
-- Users can view audit records for projects they have access to
CREATE POLICY "Users can view risk register audit for accessible projects" ON public.risk_register_audit FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert risk register audit records" ON public.risk_register_audit FOR INSERT TO authenticated
WITH
	CHECK (true);

-- Approved Changes Audit Policies
-- Users can view audit records for projects they have access to
CREATE POLICY "Users can view approved changes audit for accessible projects" ON public.approved_changes_audit FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert approved changes audit records" ON public.approved_changes_audit FOR INSERT TO authenticated
WITH
	CHECK (true);

-- ============================================================================
-- BUDGET LINE ITEM AUDIT RLS POLICIES
-- ============================================================================
-- Enable RLS
ALTER TABLE public.budget_line_item_audit_new ENABLE ROW LEVEL SECURITY;

-- Users can view audit records for projects they have access to
CREATE POLICY "Users can view budget line item audit for accessible projects" ON public.budget_line_item_audit_new FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert budget line item audit records" ON public.budget_line_item_audit_new FOR INSERT TO authenticated
WITH
	CHECK (true);

-- ============================================================================
-- BUDGET LINE ITEM AUDIT GRANTS
-- ============================================================================
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.budget_line_item_audit_new TO service_role;
