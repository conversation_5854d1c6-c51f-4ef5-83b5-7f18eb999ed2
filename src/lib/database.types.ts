export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
	graphql_public: {
		Tables: {
			[_ in never]: never;
		};
		Views: {
			[_ in never]: never;
		};
		Functions: {
			graphql: {
				Args: {
					operationName?: string;
					query?: string;
					variables?: Json;
					extensions?: Json;
				};
				Returns: Json;
			};
		};
		Enums: {
			[_ in never]: never;
		};
		CompositeTypes: {
			[_ in never]: never;
		};
	};
	public: {
		Tables: {
			approved_changes: {
				Row: {
					approved_by_user_id: string | null;
					approved_change_id: number;
					cause: string | null;
					created_at: string;
					date_approved: string;
					date_for_review: string | null;
					date_identified: string;
					description: string;
					effect: string | null;
					mitigation_plan: string | null;
					original_risk_id: number | null;
					potential_impact: number | null;
					program_impact: string | null;
					project_id: string;
					risk_owner_email: string | null;
					risk_owner_name: string | null;
					risk_owner_user_id: string | null;
					status: string;
					title: string;
					updated_at: string;
					wbs_library_item_id: string | null;
				};
				Insert: {
					approved_by_user_id?: string | null;
					approved_change_id?: number;
					cause?: string | null;
					created_at?: string;
					date_approved?: string;
					date_for_review?: string | null;
					date_identified?: string;
					description: string;
					effect?: string | null;
					mitigation_plan?: string | null;
					original_risk_id?: number | null;
					potential_impact?: number | null;
					program_impact?: string | null;
					project_id: string;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string;
					title: string;
					updated_at?: string;
					wbs_library_item_id?: string | null;
				};
				Update: {
					approved_by_user_id?: string | null;
					approved_change_id?: number;
					cause?: string | null;
					created_at?: string;
					date_approved?: string;
					date_for_review?: string | null;
					date_identified?: string;
					description?: string;
					effect?: string | null;
					mitigation_plan?: string | null;
					original_risk_id?: number | null;
					potential_impact?: number | null;
					program_impact?: string | null;
					project_id?: string;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string;
					title?: string;
					updated_at?: string;
					wbs_library_item_id?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'approved_changes_approved_by_user_id_fkey';
						columns: ['approved_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'approved_changes_original_risk_id_fkey';
						columns: ['original_risk_id'];
						isOneToOne: false;
						referencedRelation: 'risk_register';
						referencedColumns: ['risk_id'];
					},
					{
						foreignKeyName: 'approved_changes_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
					{
						foreignKeyName: 'approved_changes_risk_owner_user_id_fkey';
						columns: ['risk_owner_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'approved_changes_wbs_library_item_id_fkey';
						columns: ['wbs_library_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
				];
			};
			approved_changes_audit: {
				Row: {
					approved_by_user_id: string | null;
					approved_change_id: number | null;
					audit_id: string;
					cause: string | null;
					changed_at: string;
					changed_by: string;
					created_at: string | null;
					date_approved: string | null;
					date_for_review: string | null;
					date_identified: string | null;
					description: string | null;
					effect: string | null;
					mitigation_plan: string | null;
					new_values: Json | null;
					old_values: Json | null;
					operation_type: string;
					original_risk_id: number | null;
					potential_impact: number | null;
					program_impact: string | null;
					project_id: string | null;
					risk_owner_email: string | null;
					risk_owner_name: string | null;
					risk_owner_user_id: string | null;
					status: string | null;
					title: string | null;
					updated_at: string | null;
					wbs_library_item_id: string | null;
				};
				Insert: {
					approved_by_user_id?: string | null;
					approved_change_id?: number | null;
					audit_id?: string;
					cause?: string | null;
					changed_at?: string;
					changed_by: string;
					created_at?: string | null;
					date_approved?: string | null;
					date_for_review?: string | null;
					date_identified?: string | null;
					description?: string | null;
					effect?: string | null;
					mitigation_plan?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type: string;
					original_risk_id?: number | null;
					potential_impact?: number | null;
					program_impact?: string | null;
					project_id?: string | null;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string | null;
					title?: string | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Update: {
					approved_by_user_id?: string | null;
					approved_change_id?: number | null;
					audit_id?: string;
					cause?: string | null;
					changed_at?: string;
					changed_by?: string;
					created_at?: string | null;
					date_approved?: string | null;
					date_for_review?: string | null;
					date_identified?: string | null;
					description?: string | null;
					effect?: string | null;
					mitigation_plan?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type?: string;
					original_risk_id?: number | null;
					potential_impact?: number | null;
					program_impact?: string | null;
					project_id?: string | null;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string | null;
					title?: string | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'approved_changes_audit_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			budget_line_item_audit: {
				Row: {
					budget_line_item_audit_id: number;
					change_reason: string | null;
					change_timestamp: string;
					changed_by_user_id: string;
					created_at: string;
					new_cost_certainty: number | null;
					new_design_certainty: number | null;
					new_factor: number | null;
					new_labor_rate: number | null;
					new_material_rate: number | null;
					new_productivity_per_hour: number | null;
					new_quantity: number | null;
					new_remarks: string | null;
					new_unit: string | null;
					new_unit_rate: number | null;
					new_unit_rate_manual_override: boolean | null;
					old_cost_certainty: number | null;
					old_design_certainty: number | null;
					old_factor: number | null;
					old_labor_rate: number | null;
					old_material_rate: number | null;
					old_productivity_per_hour: number | null;
					old_quantity: number | null;
					old_remarks: string | null;
					old_unit: string | null;
					old_unit_rate: number | null;
					old_unit_rate_manual_override: boolean | null;
					project_id: string;
					wbs_library_item_id: string;
				};
				Insert: {
					budget_line_item_audit_id?: never;
					change_reason?: string | null;
					change_timestamp?: string;
					changed_by_user_id: string;
					created_at?: string;
					new_cost_certainty?: number | null;
					new_design_certainty?: number | null;
					new_factor?: number | null;
					new_labor_rate?: number | null;
					new_material_rate?: number | null;
					new_productivity_per_hour?: number | null;
					new_quantity?: number | null;
					new_remarks?: string | null;
					new_unit?: string | null;
					new_unit_rate?: number | null;
					new_unit_rate_manual_override?: boolean | null;
					old_cost_certainty?: number | null;
					old_design_certainty?: number | null;
					old_factor?: number | null;
					old_labor_rate?: number | null;
					old_material_rate?: number | null;
					old_productivity_per_hour?: number | null;
					old_quantity?: number | null;
					old_remarks?: string | null;
					old_unit?: string | null;
					old_unit_rate?: number | null;
					old_unit_rate_manual_override?: boolean | null;
					project_id: string;
					wbs_library_item_id: string;
				};
				Update: {
					budget_line_item_audit_id?: never;
					change_reason?: string | null;
					change_timestamp?: string;
					changed_by_user_id?: string;
					created_at?: string;
					new_cost_certainty?: number | null;
					new_design_certainty?: number | null;
					new_factor?: number | null;
					new_labor_rate?: number | null;
					new_material_rate?: number | null;
					new_productivity_per_hour?: number | null;
					new_quantity?: number | null;
					new_remarks?: string | null;
					new_unit?: string | null;
					new_unit_rate?: number | null;
					new_unit_rate_manual_override?: boolean | null;
					old_cost_certainty?: number | null;
					old_design_certainty?: number | null;
					old_factor?: number | null;
					old_labor_rate?: number | null;
					old_material_rate?: number | null;
					old_productivity_per_hour?: number | null;
					old_quantity?: number | null;
					old_remarks?: string | null;
					old_unit?: string | null;
					old_unit_rate?: number | null;
					old_unit_rate_manual_override?: boolean | null;
					project_id?: string;
					wbs_library_item_id?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'budget_line_item_audit_changed_by_user_id_fkey';
						columns: ['changed_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'budget_line_item_audit_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
					{
						foreignKeyName: 'budget_line_item_audit_wbs_library_item_id_fkey';
						columns: ['wbs_library_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
				];
			};
			budget_line_item_audit_new: {
				Row: {
					audit_id: string;
					budget_line_item_id: number | null;
					changed_at: string;
					changed_by: string;
					cost_certainty: number | null;
					created_at: string | null;
					design_certainty: number | null;
					factor: number | null;
					labor_rate: number | null;
					material_rate: number | null;
					new_values: Json | null;
					old_values: Json | null;
					operation_type: string;
					productivity_per_hour: number | null;
					project_id: string | null;
					quantity: number | null;
					remarks: string | null;
					unit: string | null;
					unit_rate: number | null;
					unit_rate_manual_override: boolean | null;
					updated_at: string | null;
					wbs_library_item_id: string | null;
				};
				Insert: {
					audit_id?: string;
					budget_line_item_id?: number | null;
					changed_at?: string;
					changed_by: string;
					cost_certainty?: number | null;
					created_at?: string | null;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate?: number | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type: string;
					productivity_per_hour?: number | null;
					project_id?: string | null;
					quantity?: number | null;
					remarks?: string | null;
					unit?: string | null;
					unit_rate?: number | null;
					unit_rate_manual_override?: boolean | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Update: {
					audit_id?: string;
					budget_line_item_id?: number | null;
					changed_at?: string;
					changed_by?: string;
					cost_certainty?: number | null;
					created_at?: string | null;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate?: number | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type?: string;
					productivity_per_hour?: number | null;
					project_id?: string | null;
					quantity?: number | null;
					remarks?: string | null;
					unit?: string | null;
					unit_rate?: number | null;
					unit_rate_manual_override?: boolean | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'budget_line_item_audit_new_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			budget_line_item_current: {
				Row: {
					budget_line_item_id: number;
					cost_certainty: number | null;
					created_at: string;
					design_certainty: number | null;
					factor: number | null;
					labor_rate: number | null;
					material_rate: number;
					productivity_per_hour: number | null;
					project_id: string;
					quantity: number;
					remarks: string | null;
					unit: string | null;
					unit_rate: number;
					unit_rate_manual_override: boolean;
					updated_at: string;
					wbs_library_item_id: string;
				};
				Insert: {
					budget_line_item_id?: never;
					cost_certainty?: number | null;
					created_at?: string;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate: number;
					productivity_per_hour?: number | null;
					project_id: string;
					quantity: number;
					remarks?: string | null;
					unit?: string | null;
					unit_rate: number;
					unit_rate_manual_override?: boolean;
					updated_at?: string;
					wbs_library_item_id: string;
				};
				Update: {
					budget_line_item_id?: never;
					cost_certainty?: number | null;
					created_at?: string;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate?: number;
					productivity_per_hour?: number | null;
					project_id?: string;
					quantity?: number;
					remarks?: string | null;
					unit?: string | null;
					unit_rate?: number;
					unit_rate_manual_override?: boolean;
					updated_at?: string;
					wbs_library_item_id?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'budget_line_item_current_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
					{
						foreignKeyName: 'budget_line_item_current_wbs_library_item_id_fkey';
						columns: ['wbs_library_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
				];
			};
			budget_snapshot: {
				Row: {
					budget_snapshot_id: number;
					created_at: string;
					created_by_user_id: string;
					freeze_date: string;
					freeze_reason: string | null;
					project_stage_id: number;
					updated_at: string;
				};
				Insert: {
					budget_snapshot_id?: never;
					created_at?: string;
					created_by_user_id: string;
					freeze_date?: string;
					freeze_reason?: string | null;
					project_stage_id: number;
					updated_at?: string;
				};
				Update: {
					budget_snapshot_id?: never;
					created_at?: string;
					created_by_user_id?: string;
					freeze_date?: string;
					freeze_reason?: string | null;
					project_stage_id?: number;
					updated_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'budget_snapshot_created_by_user_id_fkey';
						columns: ['created_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'budget_snapshot_project_stage_id_fkey';
						columns: ['project_stage_id'];
						isOneToOne: false;
						referencedRelation: 'project_stage';
						referencedColumns: ['project_stage_id'];
					},
				];
			};
			budget_snapshot_line_item: {
				Row: {
					budget_snapshot_id: number;
					budget_snapshot_line_item_id: number;
					cost_certainty: number | null;
					created_at: string;
					design_certainty: number | null;
					factor: number | null;
					labor_rate: number | null;
					material_rate: number | null;
					productivity_per_hour: number | null;
					quantity: number | null;
					remarks: string | null;
					unit: string | null;
					unit_rate: number | null;
					unit_rate_manual_override: boolean;
					updated_at: string;
					wbs_library_item_id: string;
				};
				Insert: {
					budget_snapshot_id: number;
					budget_snapshot_line_item_id?: never;
					cost_certainty?: number | null;
					created_at?: string;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate?: number | null;
					productivity_per_hour?: number | null;
					quantity?: number | null;
					remarks?: string | null;
					unit?: string | null;
					unit_rate?: number | null;
					unit_rate_manual_override?: boolean;
					updated_at?: string;
					wbs_library_item_id: string;
				};
				Update: {
					budget_snapshot_id?: number;
					budget_snapshot_line_item_id?: never;
					cost_certainty?: number | null;
					created_at?: string;
					design_certainty?: number | null;
					factor?: number | null;
					labor_rate?: number | null;
					material_rate?: number | null;
					productivity_per_hour?: number | null;
					quantity?: number | null;
					remarks?: string | null;
					unit?: string | null;
					unit_rate?: number | null;
					unit_rate_manual_override?: boolean;
					updated_at?: string;
					wbs_library_item_id?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'budget_snapshot_line_item_budget_snapshot_id_fkey';
						columns: ['budget_snapshot_id'];
						isOneToOne: false;
						referencedRelation: 'budget_snapshot';
						referencedColumns: ['budget_snapshot_id'];
					},
					{
						foreignKeyName: 'budget_snapshot_line_item_wbs_library_item_id_fkey';
						columns: ['wbs_library_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
				];
			};
			client: {
				Row: {
					client_id: string;
					client_url: string | null;
					created_at: string;
					created_by_user_id: string;
					description: string | null;
					internal_url: string | null;
					internal_url_description: string | null;
					logo_url: string | null;
					name: string;
					org_id: string;
					updated_at: string;
				};
				Insert: {
					client_id?: string;
					client_url?: string | null;
					created_at?: string;
					created_by_user_id: string;
					description?: string | null;
					internal_url?: string | null;
					internal_url_description?: string | null;
					logo_url?: string | null;
					name: string;
					org_id: string;
					updated_at?: string;
				};
				Update: {
					client_id?: string;
					client_url?: string | null;
					created_at?: string;
					created_by_user_id?: string;
					description?: string | null;
					internal_url?: string | null;
					internal_url_description?: string | null;
					logo_url?: string | null;
					name?: string;
					org_id?: string;
					updated_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'client_created_by_user_id_fkey';
						columns: ['created_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'client_org_id_fkey';
						columns: ['org_id'];
						isOneToOne: false;
						referencedRelation: 'organization';
						referencedColumns: ['org_id'];
					},
				];
			};
			gateway_checklist_item: {
				Row: {
					created_at: string;
					description: string | null;
					gateway_checklist_item_id: number;
					name: string;
					project_stage_id: number;
					updated_at: string;
				};
				Insert: {
					created_at?: string;
					description?: string | null;
					gateway_checklist_item_id?: never;
					name: string;
					project_stage_id: number;
					updated_at?: string;
				};
				Update: {
					created_at?: string;
					description?: string | null;
					gateway_checklist_item_id?: never;
					name?: string;
					project_stage_id?: number;
					updated_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'gateway_checklist_item_project_stage_id_fkey';
						columns: ['project_stage_id'];
						isOneToOne: false;
						referencedRelation: 'project_stage';
						referencedColumns: ['project_stage_id'];
					},
				];
			};
			gateway_checklist_item_audit: {
				Row: {
					audit_id: string;
					changed_at: string;
					changed_by: string;
					created_at: string | null;
					description: string | null;
					gateway_checklist_item_id: number | null;
					name: string | null;
					new_values: Json | null;
					old_values: Json | null;
					operation_type: string;
					project_stage_id: number | null;
					updated_at: string | null;
				};
				Insert: {
					audit_id?: string;
					changed_at?: string;
					changed_by: string;
					created_at?: string | null;
					description?: string | null;
					gateway_checklist_item_id?: number | null;
					name?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type: string;
					project_stage_id?: number | null;
					updated_at?: string | null;
				};
				Update: {
					audit_id?: string;
					changed_at?: string;
					changed_by?: string;
					created_at?: string | null;
					description?: string | null;
					gateway_checklist_item_id?: number | null;
					name?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type?: string;
					project_stage_id?: number | null;
					updated_at?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'gateway_checklist_item_audit_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			gateway_checklist_item_status_log: {
				Row: {
					created_at: string;
					gateway_checklist_item_id: number;
					latest: boolean;
					log_id: number;
					status: Database['public']['Enums']['checklist_item_status'];
					updated_at: string;
					updated_by_user_id: string;
					valid_at: string;
				};
				Insert: {
					created_at?: string;
					gateway_checklist_item_id: number;
					latest?: boolean;
					log_id?: never;
					status?: Database['public']['Enums']['checklist_item_status'];
					updated_at?: string;
					updated_by_user_id?: string;
					valid_at?: string;
				};
				Update: {
					created_at?: string;
					gateway_checklist_item_id?: number;
					latest?: boolean;
					log_id?: never;
					status?: Database['public']['Enums']['checklist_item_status'];
					updated_at?: string;
					updated_by_user_id?: string;
					valid_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'gateway_checklist_item_status_lo_gateway_checklist_item_id_fkey';
						columns: ['gateway_checklist_item_id'];
						isOneToOne: false;
						referencedRelation: 'gateway_checklist_item';
						referencedColumns: ['gateway_checklist_item_id'];
					},
					{
						foreignKeyName: 'gateway_checklist_item_status_log_updated_by_user_id_fkey';
						columns: ['updated_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			invite: {
				Row: {
					created_at: string;
					expires_at: string;
					invite_id: string;
					invitee_email: string;
					inviter_id: string;
					resource_id: string;
					resource_type: Database['public']['Enums']['invite_resource_type'];
					role: string;
					status: Database['public']['Enums']['invite_status'];
					token_hash: string;
					updated_at: string;
					updated_by: string | null;
				};
				Insert: {
					created_at?: string;
					expires_at: string;
					invite_id?: string;
					invitee_email: string;
					inviter_id: string;
					resource_id: string;
					resource_type: Database['public']['Enums']['invite_resource_type'];
					role: string;
					status?: Database['public']['Enums']['invite_status'];
					token_hash: string;
					updated_at?: string;
					updated_by?: string | null;
				};
				Update: {
					created_at?: string;
					expires_at?: string;
					invite_id?: string;
					invitee_email?: string;
					inviter_id?: string;
					resource_id?: string;
					resource_type?: Database['public']['Enums']['invite_resource_type'];
					role?: string;
					status?: Database['public']['Enums']['invite_status'];
					token_hash?: string;
					updated_at?: string;
					updated_by?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'invite_inviter_id_fkey';
						columns: ['inviter_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'invite_updated_by_fkey';
						columns: ['updated_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			membership: {
				Row: {
					created_at: string;
					entity_id: string;
					entity_type: Database['public']['Enums']['entity_type'];
					membership_id: number;
					role: Database['public']['Enums']['membership_role'];
					updated_at: string;
					user_id: string;
				};
				Insert: {
					created_at?: string;
					entity_id: string;
					entity_type: Database['public']['Enums']['entity_type'];
					membership_id?: never;
					role: Database['public']['Enums']['membership_role'];
					updated_at?: string;
					user_id: string;
				};
				Update: {
					created_at?: string;
					entity_id?: string;
					entity_type?: Database['public']['Enums']['entity_type'];
					membership_id?: never;
					role?: Database['public']['Enums']['membership_role'];
					updated_at?: string;
					user_id?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'membership_user_id_fkey';
						columns: ['user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			organization: {
				Row: {
					created_at: string;
					created_by_user_id: string;
					description: string | null;
					logo_url: string | null;
					name: string;
					org_id: string;
					updated_at: string;
				};
				Insert: {
					created_at?: string;
					created_by_user_id?: string;
					description?: string | null;
					logo_url?: string | null;
					name: string;
					org_id?: string;
					updated_at?: string;
				};
				Update: {
					created_at?: string;
					created_by_user_id?: string;
					description?: string | null;
					logo_url?: string | null;
					name?: string;
					org_id?: string;
					updated_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'organization_created_by_user_id_fkey';
						columns: ['created_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			profile: {
				Row: {
					avatar_url: string | null;
					created_at: string;
					email: string;
					full_name: string | null;
					updated_at: string;
					user_id: string;
				};
				Insert: {
					avatar_url?: string | null;
					created_at?: string;
					email: string;
					full_name?: string | null;
					updated_at?: string;
					user_id: string;
				};
				Update: {
					avatar_url?: string | null;
					created_at?: string;
					email?: string;
					full_name?: string | null;
					updated_at?: string;
					user_id?: string;
				};
				Relationships: [];
			};
			project: {
				Row: {
					client_id: string;
					created_at: string;
					created_by_user_id: string;
					description: string | null;
					name: string;
					project_id: string;
					updated_at: string;
					wbs_library_id: number;
				};
				Insert: {
					client_id: string;
					created_at?: string;
					created_by_user_id?: string;
					description?: string | null;
					name: string;
					project_id?: string;
					updated_at?: string;
					wbs_library_id: number;
				};
				Update: {
					client_id?: string;
					created_at?: string;
					created_by_user_id?: string;
					description?: string | null;
					name?: string;
					project_id?: string;
					updated_at?: string;
					wbs_library_id?: number;
				};
				Relationships: [
					{
						foreignKeyName: 'project_client_id_fkey';
						columns: ['client_id'];
						isOneToOne: false;
						referencedRelation: 'client';
						referencedColumns: ['client_id'];
					},
					{
						foreignKeyName: 'project_created_by_user_id_fkey';
						columns: ['created_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'project_wbs_library_id_fkey';
						columns: ['wbs_library_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library';
						referencedColumns: ['wbs_library_id'];
					},
				];
			};
			project_gateway_stage_info: {
				Row: {
					above_ground_floors: number | null;
					additional_data: Json | null;
					ancillary_areas: number | null;
					area_of_lowest_floor: number | null;
					average_storey_height: number | null;
					basement_floors: number | null;
					basement_storeys_included_above: number | null;
					below_ground_floors: number | null;
					circulation_area: number | null;
					created_at: string;
					created_by_user_id: string;
					external_vertical_envelope: number | null;
					ground_floor: number | null;
					ground_floor_height: number | null;
					internal_cube: number | null;
					internal_divisions: number | null;
					nr_of_storeys: number | null;
					nr_of_storeys_primary: number | null;
					nr_of_storeys_secondary: number | null;
					number_of_units: number | null;
					project_gateway_stage_info_id: number;
					project_stage_id: number;
					site_area: number | null;
					spaces_not_enclosed: number | null;
					total_gross_internal_floor_area: number | null;
					total_gross_internal_floor_area_2: number | null;
					updated_at: string;
					upper_floors: number | null;
					usable_area: number | null;
				};
				Insert: {
					above_ground_floors?: number | null;
					additional_data?: Json | null;
					ancillary_areas?: number | null;
					area_of_lowest_floor?: number | null;
					average_storey_height?: number | null;
					basement_floors?: number | null;
					basement_storeys_included_above?: number | null;
					below_ground_floors?: number | null;
					circulation_area?: number | null;
					created_at?: string;
					created_by_user_id?: string;
					external_vertical_envelope?: number | null;
					ground_floor?: number | null;
					ground_floor_height?: number | null;
					internal_cube?: number | null;
					internal_divisions?: number | null;
					nr_of_storeys?: number | null;
					nr_of_storeys_primary?: number | null;
					nr_of_storeys_secondary?: number | null;
					number_of_units?: number | null;
					project_gateway_stage_info_id?: never;
					project_stage_id: number;
					site_area?: number | null;
					spaces_not_enclosed?: number | null;
					total_gross_internal_floor_area?: number | null;
					total_gross_internal_floor_area_2?: number | null;
					updated_at?: string;
					upper_floors?: number | null;
					usable_area?: number | null;
				};
				Update: {
					above_ground_floors?: number | null;
					additional_data?: Json | null;
					ancillary_areas?: number | null;
					area_of_lowest_floor?: number | null;
					average_storey_height?: number | null;
					basement_floors?: number | null;
					basement_storeys_included_above?: number | null;
					below_ground_floors?: number | null;
					circulation_area?: number | null;
					created_at?: string;
					created_by_user_id?: string;
					external_vertical_envelope?: number | null;
					ground_floor?: number | null;
					ground_floor_height?: number | null;
					internal_cube?: number | null;
					internal_divisions?: number | null;
					nr_of_storeys?: number | null;
					nr_of_storeys_primary?: number | null;
					nr_of_storeys_secondary?: number | null;
					number_of_units?: number | null;
					project_gateway_stage_info_id?: never;
					project_stage_id?: number;
					site_area?: number | null;
					spaces_not_enclosed?: number | null;
					total_gross_internal_floor_area?: number | null;
					total_gross_internal_floor_area_2?: number | null;
					updated_at?: string;
					upper_floors?: number | null;
					usable_area?: number | null;
				};
				Relationships: [
					{
						foreignKeyName: 'project_gateway_stage_info_created_by_user_id_fkey';
						columns: ['created_by_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'project_gateway_stage_info_project_stage_id_fkey';
						columns: ['project_stage_id'];
						isOneToOne: true;
						referencedRelation: 'project_stage';
						referencedColumns: ['project_stage_id'];
					},
				];
			};
			project_gateway_stage_info_audit: {
				Row: {
					above_ground_floors: number | null;
					additional_data: Json | null;
					ancillary_areas: number | null;
					area_of_lowest_floor: number | null;
					audit_id: string;
					average_storey_height: number | null;
					basement_floors: number | null;
					basement_storeys_included_above: number | null;
					below_ground_floors: number | null;
					changed_at: string;
					changed_by: string;
					circulation_area: number | null;
					created_at: string | null;
					created_by_user_id: string | null;
					external_vertical_envelope: number | null;
					ground_floor: number | null;
					ground_floor_height: number | null;
					internal_cube: number | null;
					internal_divisions: number | null;
					new_values: Json | null;
					nr_of_storeys: number | null;
					nr_of_storeys_primary: number | null;
					nr_of_storeys_secondary: number | null;
					number_of_units: number | null;
					old_values: Json | null;
					operation_type: string;
					project_gateway_stage_info_id: number | null;
					project_stage_id: number | null;
					site_area: number | null;
					spaces_not_enclosed: number | null;
					total_gross_internal_floor_area: number | null;
					total_gross_internal_floor_area_2: number | null;
					updated_at: string | null;
					upper_floors: number | null;
					usable_area: number | null;
				};
				Insert: {
					above_ground_floors?: number | null;
					additional_data?: Json | null;
					ancillary_areas?: number | null;
					area_of_lowest_floor?: number | null;
					audit_id?: string;
					average_storey_height?: number | null;
					basement_floors?: number | null;
					basement_storeys_included_above?: number | null;
					below_ground_floors?: number | null;
					changed_at?: string;
					changed_by: string;
					circulation_area?: number | null;
					created_at?: string | null;
					created_by_user_id?: string | null;
					external_vertical_envelope?: number | null;
					ground_floor?: number | null;
					ground_floor_height?: number | null;
					internal_cube?: number | null;
					internal_divisions?: number | null;
					new_values?: Json | null;
					nr_of_storeys?: number | null;
					nr_of_storeys_primary?: number | null;
					nr_of_storeys_secondary?: number | null;
					number_of_units?: number | null;
					old_values?: Json | null;
					operation_type: string;
					project_gateway_stage_info_id?: number | null;
					project_stage_id?: number | null;
					site_area?: number | null;
					spaces_not_enclosed?: number | null;
					total_gross_internal_floor_area?: number | null;
					total_gross_internal_floor_area_2?: number | null;
					updated_at?: string | null;
					upper_floors?: number | null;
					usable_area?: number | null;
				};
				Update: {
					above_ground_floors?: number | null;
					additional_data?: Json | null;
					ancillary_areas?: number | null;
					area_of_lowest_floor?: number | null;
					audit_id?: string;
					average_storey_height?: number | null;
					basement_floors?: number | null;
					basement_storeys_included_above?: number | null;
					below_ground_floors?: number | null;
					changed_at?: string;
					changed_by?: string;
					circulation_area?: number | null;
					created_at?: string | null;
					created_by_user_id?: string | null;
					external_vertical_envelope?: number | null;
					ground_floor?: number | null;
					ground_floor_height?: number | null;
					internal_cube?: number | null;
					internal_divisions?: number | null;
					new_values?: Json | null;
					nr_of_storeys?: number | null;
					nr_of_storeys_primary?: number | null;
					nr_of_storeys_secondary?: number | null;
					number_of_units?: number | null;
					old_values?: Json | null;
					operation_type?: string;
					project_gateway_stage_info_id?: number | null;
					project_stage_id?: number | null;
					site_area?: number | null;
					spaces_not_enclosed?: number | null;
					total_gross_internal_floor_area?: number | null;
					total_gross_internal_floor_area_2?: number | null;
					updated_at?: string | null;
					upper_floors?: number | null;
					usable_area?: number | null;
				};
				Relationships: [
					{
						foreignKeyName: 'project_gateway_stage_info_audit_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			project_stage: {
				Row: {
					completion_notes: string | null;
					created_at: string;
					date_completed: string | null;
					date_started: string | null;
					description: string | null;
					gateway_qualitative_scorecard: Json | null;
					name: string;
					project_id: string;
					project_stage_id: number;
					stage: number | null;
					stage_order: number;
					updated_at: string;
				};
				Insert: {
					completion_notes?: string | null;
					created_at?: string;
					date_completed?: string | null;
					date_started?: string | null;
					description?: string | null;
					gateway_qualitative_scorecard?: Json | null;
					name: string;
					project_id: string;
					project_stage_id?: never;
					stage?: number | null;
					stage_order: number;
					updated_at?: string;
				};
				Update: {
					completion_notes?: string | null;
					created_at?: string;
					date_completed?: string | null;
					date_started?: string | null;
					description?: string | null;
					gateway_qualitative_scorecard?: Json | null;
					name?: string;
					project_id?: string;
					project_stage_id?: never;
					stage?: number | null;
					stage_order?: number;
					updated_at?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'project_stage_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
				];
			};
			project_stage_audit: {
				Row: {
					audit_id: string;
					changed_at: string;
					changed_by: string;
					completion_notes: string | null;
					created_at: string | null;
					date_completed: string | null;
					date_started: string | null;
					description: string | null;
					gateway_qualitative_scorecard: Json | null;
					name: string | null;
					new_values: Json | null;
					old_values: Json | null;
					operation_type: string;
					project_id: string | null;
					project_stage_id: number | null;
					stage: number | null;
					stage_order: number | null;
					updated_at: string | null;
				};
				Insert: {
					audit_id?: string;
					changed_at?: string;
					changed_by: string;
					completion_notes?: string | null;
					created_at?: string | null;
					date_completed?: string | null;
					date_started?: string | null;
					description?: string | null;
					gateway_qualitative_scorecard?: Json | null;
					name?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type: string;
					project_id?: string | null;
					project_stage_id?: number | null;
					stage?: number | null;
					stage_order?: number | null;
					updated_at?: string | null;
				};
				Update: {
					audit_id?: string;
					changed_at?: string;
					changed_by?: string;
					completion_notes?: string | null;
					created_at?: string | null;
					date_completed?: string | null;
					date_started?: string | null;
					description?: string | null;
					gateway_qualitative_scorecard?: Json | null;
					name?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type?: string;
					project_id?: string | null;
					project_stage_id?: number | null;
					stage?: number | null;
					stage_order?: number | null;
					updated_at?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'project_stage_audit_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			risk_register: {
				Row: {
					cause: string | null;
					created_at: string;
					date_for_review: string | null;
					date_identified: string;
					description: string;
					effect: string | null;
					mitigation_plan: string | null;
					potential_impact: number | null;
					probability: number;
					program_impact: string | null;
					project_id: string;
					risk_id: number;
					risk_owner_email: string | null;
					risk_owner_name: string | null;
					risk_owner_user_id: string | null;
					status: string;
					title: string;
					updated_at: string;
					wbs_library_item_id: string | null;
				};
				Insert: {
					cause?: string | null;
					created_at?: string;
					date_for_review?: string | null;
					date_identified?: string;
					description: string;
					effect?: string | null;
					mitigation_plan?: string | null;
					potential_impact?: number | null;
					probability?: number;
					program_impact?: string | null;
					project_id: string;
					risk_id?: number;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string;
					title: string;
					updated_at?: string;
					wbs_library_item_id?: string | null;
				};
				Update: {
					cause?: string | null;
					created_at?: string;
					date_for_review?: string | null;
					date_identified?: string;
					description?: string;
					effect?: string | null;
					mitigation_plan?: string | null;
					potential_impact?: number | null;
					probability?: number;
					program_impact?: string | null;
					project_id?: string;
					risk_id?: number;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string;
					title?: string;
					updated_at?: string;
					wbs_library_item_id?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'risk_register_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
					{
						foreignKeyName: 'risk_register_risk_owner_user_id_fkey';
						columns: ['risk_owner_user_id'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
					{
						foreignKeyName: 'risk_register_wbs_library_item_id_fkey';
						columns: ['wbs_library_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
				];
			};
			risk_register_audit: {
				Row: {
					audit_id: string;
					cause: string | null;
					changed_at: string;
					changed_by: string;
					created_at: string | null;
					date_for_review: string | null;
					date_identified: string | null;
					description: string | null;
					effect: string | null;
					mitigation_plan: string | null;
					new_values: Json | null;
					old_values: Json | null;
					operation_type: string;
					potential_impact: number | null;
					probability: number | null;
					program_impact: string | null;
					project_id: string | null;
					risk_id: number | null;
					risk_owner_email: string | null;
					risk_owner_name: string | null;
					risk_owner_user_id: string | null;
					status: string | null;
					title: string | null;
					updated_at: string | null;
					wbs_library_item_id: string | null;
				};
				Insert: {
					audit_id?: string;
					cause?: string | null;
					changed_at?: string;
					changed_by: string;
					created_at?: string | null;
					date_for_review?: string | null;
					date_identified?: string | null;
					description?: string | null;
					effect?: string | null;
					mitigation_plan?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type: string;
					potential_impact?: number | null;
					probability?: number | null;
					program_impact?: string | null;
					project_id?: string | null;
					risk_id?: number | null;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string | null;
					title?: string | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Update: {
					audit_id?: string;
					cause?: string | null;
					changed_at?: string;
					changed_by?: string;
					created_at?: string | null;
					date_for_review?: string | null;
					date_identified?: string | null;
					description?: string | null;
					effect?: string | null;
					mitigation_plan?: string | null;
					new_values?: Json | null;
					old_values?: Json | null;
					operation_type?: string;
					potential_impact?: number | null;
					probability?: number | null;
					program_impact?: string | null;
					project_id?: string | null;
					risk_id?: number | null;
					risk_owner_email?: string | null;
					risk_owner_name?: string | null;
					risk_owner_user_id?: string | null;
					status?: string | null;
					title?: string | null;
					updated_at?: string | null;
					wbs_library_item_id?: string | null;
				};
				Relationships: [
					{
						foreignKeyName: 'risk_register_audit_changed_by_fkey';
						columns: ['changed_by'];
						isOneToOne: false;
						referencedRelation: 'profile';
						referencedColumns: ['user_id'];
					},
				];
			};
			wbs_library: {
				Row: {
					created_at: string;
					description: string | null;
					name: string;
					updated_at: string;
					wbs_library_id: number;
				};
				Insert: {
					created_at?: string;
					description?: string | null;
					name: string;
					updated_at?: string;
					wbs_library_id?: never;
				};
				Update: {
					created_at?: string;
					description?: string | null;
					name?: string;
					updated_at?: string;
					wbs_library_id?: never;
				};
				Relationships: [];
			};
			wbs_library_item: {
				Row: {
					client_id: string | null;
					code: string;
					cost_scope: string | null;
					created_at: string;
					description: string;
					in_level_code: string;
					item_type: Database['public']['Enums']['wbs_item_type'];
					level: number;
					parent_item_id: string | null;
					project_id: string | null;
					updated_at: string;
					wbs_library_id: number;
					wbs_library_item_id: string;
				};
				Insert: {
					client_id?: string | null;
					code: string;
					cost_scope?: string | null;
					created_at?: string;
					description: string;
					in_level_code: string;
					item_type?: Database['public']['Enums']['wbs_item_type'];
					level: number;
					parent_item_id?: string | null;
					project_id?: string | null;
					updated_at?: string;
					wbs_library_id: number;
					wbs_library_item_id?: string;
				};
				Update: {
					client_id?: string | null;
					code?: string;
					cost_scope?: string | null;
					created_at?: string;
					description?: string;
					in_level_code?: string;
					item_type?: Database['public']['Enums']['wbs_item_type'];
					level?: number;
					parent_item_id?: string | null;
					project_id?: string | null;
					updated_at?: string;
					wbs_library_id?: number;
					wbs_library_item_id?: string;
				};
				Relationships: [
					{
						foreignKeyName: 'wbs_library_item_client_id_fkey';
						columns: ['client_id'];
						isOneToOne: false;
						referencedRelation: 'client';
						referencedColumns: ['client_id'];
					},
					{
						foreignKeyName: 'wbs_library_item_parent_item_id_fkey';
						columns: ['parent_item_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library_item';
						referencedColumns: ['wbs_library_item_id'];
					},
					{
						foreignKeyName: 'wbs_library_item_project_id_fkey';
						columns: ['project_id'];
						isOneToOne: false;
						referencedRelation: 'project';
						referencedColumns: ['project_id'];
					},
					{
						foreignKeyName: 'wbs_library_item_wbs_library_id_fkey';
						columns: ['wbs_library_id'];
						isOneToOne: false;
						referencedRelation: 'wbs_library';
						referencedColumns: ['wbs_library_id'];
					},
				];
			};
		};
		Views: {
			[_ in never]: never;
		};
		Functions: {
			accept_invite: {
				Args: { token_param: string };
				Returns: Json;
			};
			calculate_unit_item_cost: {
				Args: {
					p_material_rate: number;
					p_labor_rate: number;
					p_productivity_per_hour: number;
				};
				Returns: number;
			};
			can_access_client: {
				Args: { client_id_param: string };
				Returns: boolean;
			};
			can_access_project: {
				Args: { project_id_param: string };
				Returns: boolean;
			};
			can_modify_client: {
				Args: { client_id_param: string };
				Returns: boolean;
			};
			can_modify_client_wbs: {
				Args: { client_id_param: string };
				Returns: boolean;
			};
			can_modify_project: {
				Args: { project_id_param: string };
				Returns: boolean;
			};
			compare_budget_snapshots: {
				Args: { p_snapshot_id_1: number; p_snapshot_id_2: number };
				Returns: {
					wbs_library_item_id: string;
					snapshot_1_quantity: number;
					snapshot_1_cost: number;
					snapshot_2_quantity: number;
					snapshot_2_cost: number;
					quantity_diff: number;
					cost_diff: number;
					percent_change: number;
				}[];
			};
			complete_project_stage: {
				Args: { p_project_stage_id: number; p_completion_notes?: string };
				Returns: number;
			};
			create_budget_snapshot: {
				Args: { p_project_stage_id: number; p_freeze_reason?: string };
				Returns: number;
			};
			create_organization: {
				Args: { name: string; description?: string; logo_url?: string };
				Returns: Json;
			};
			current_user_has_entity_access: {
				Args: {
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
				};
				Returns: boolean;
			};
			current_user_has_entity_role: {
				Args: {
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
					min_role_param: Database['public']['Enums']['membership_role'];
				};
				Returns: boolean;
			};
			generate_demo_budget_data: {
				Args: Record<PropertyKey, never>;
				Returns: Json;
			};
			get_client_members: {
				Args: { _client_name: string };
				Returns: {
					user_id: string;
					email: string;
					full_name: string;
					avatar_url: string;
					created_at: string;
					updated_at: string;
					role: string;
					membership_id: number;
					access_via: string;
				}[];
			};
			get_clients_with_permissions: {
				Args: { org_name_param: string };
				Returns: {
					client_id: string;
					name: string;
					description: string;
					logo_url: string;
					client_url: string;
					internal_url: string;
					internal_url_description: string;
					org_id: string;
					created_at: string;
					updated_at: string;
					created_by_user_id: string;
					organization_name: string;
					project_count: number;
					is_client_admin: boolean;
					is_org_admin: boolean;
				}[];
			};
			get_effective_role: {
				Args: {
					user_id_param: string;
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
				};
				Returns: Database['public']['Enums']['membership_role'];
			};
			get_entity_ancestors: {
				Args: {
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
				};
				Returns: {
					entity_type: Database['public']['Enums']['entity_type'];
					entity_id: string;
				}[];
			};
			get_organization_by_name: {
				Args: { org_name_param: string };
				Returns: {
					org_id: string;
					name: string;
					description: string;
					logo_url: string;
					created_by_user_id: string;
					created_at: string;
					updated_at: string;
				}[];
			};
			has_entity_access: {
				Args: {
					user_id_param: string;
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
				};
				Returns: boolean;
			};
			has_entity_role: {
				Args: {
					user_id_param: string;
					entity_type_param: Database['public']['Enums']['entity_type'];
					entity_id_param: string;
					min_role_param: Database['public']['Enums']['membership_role'];
				};
				Returns: boolean;
			};
			import_budget_data: {
				Args: { p_project_id: string; p_items: Json };
				Returns: Json;
			};
			is_client_admin: {
				Args: { client_id_param: string };
				Returns: boolean;
			};
			is_org_admin_for_project: {
				Args: { project_id_param: string };
				Returns: boolean;
			};
			is_project_owner: {
				Args: { project_id_param: string };
				Returns: boolean;
			};
			is_stage_ready_for_completion: {
				Args: { p_project_stage_id: number };
				Returns: boolean;
			};
			profiles_with_client_access: {
				Args: { _client_name: string };
				Returns: {
					user_id: string;
					email: string;
					full_name: string;
					avatar_url: string;
					created_at: string;
					updated_at: string;
					role: string;
					membership_id: number;
					access_via: string;
				}[];
			};
			profiles_with_project_access: {
				Args: { _project_name: string; _client_name: string };
				Returns: {
					user_id: string;
					email: string;
					full_name: string;
					avatar_url: string;
					created_at: string;
					updated_at: string;
					access_via: string;
					role: string;
				}[];
			};
			revert_to_budget_snapshot: {
				Args: { p_budget_snapshot_id: number; p_revert_reason?: string };
				Returns: boolean;
			};
			upsert_budget_line_item: {
				Args: {
					p_project_id: string;
					p_wbs_library_item_id: string;
					p_quantity: number;
					p_unit?: string;
					p_material_rate?: number;
					p_labor_rate?: number;
					p_productivity_per_hour?: number;
					p_unit_rate_manual_override?: boolean;
					p_unit_rate?: number;
					p_factor?: number;
					p_remarks?: string;
					p_cost_certainty?: number;
					p_design_certainty?: number;
					p_change_reason?: string;
					p_budget_line_item_id?: number;
				};
				Returns: number;
			};
		};
		Enums: {
			checklist_item_status: 'Incomplete' | 'Deferred' | 'Complete';
			entity_type: 'organization' | 'client' | 'project';
			invite_resource_type: 'organization' | 'client' | 'project';
			invite_status: 'pending' | 'accepted' | 'revoked' | 'expired' | 'declined';
			membership_role: 'viewer' | 'editor' | 'admin' | 'owner';
			wbs_item_type: 'Standard' | 'Custom';
		};
		CompositeTypes: {
			[_ in never]: never;
		};
	};
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
	DefaultSchemaTableNameOrOptions extends
		| keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
		| { schema: keyof Database },
	TableName extends DefaultSchemaTableNameOrOptions extends {
		schema: keyof Database;
	}
		? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
				Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
		: never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
	? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
			Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
			Row: infer R;
		}
		? R
		: never
	: DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
		? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
				Row: infer R;
			}
			? R
			: never
		: never;

export type TablesInsert<
	DefaultSchemaTableNameOrOptions extends
		| keyof DefaultSchema['Tables']
		| { schema: keyof Database },
	TableName extends DefaultSchemaTableNameOrOptions extends {
		schema: keyof Database;
	}
		? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
		: never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
	? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
			Insert: infer I;
		}
		? I
		: never
	: DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
		? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
				Insert: infer I;
			}
			? I
			: never
		: never;

export type TablesUpdate<
	DefaultSchemaTableNameOrOptions extends
		| keyof DefaultSchema['Tables']
		| { schema: keyof Database },
	TableName extends DefaultSchemaTableNameOrOptions extends {
		schema: keyof Database;
	}
		? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
		: never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
	? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
			Update: infer U;
		}
		? U
		: never
	: DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
		? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
				Update: infer U;
			}
			? U
			: never
		: never;

export type Enums<
	DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums'] | { schema: keyof Database },
	EnumName extends DefaultSchemaEnumNameOrOptions extends {
		schema: keyof Database;
	}
		? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
		: never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
	? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
	: DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
		? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
		: never;

export type CompositeTypes<
	PublicCompositeTypeNameOrOptions extends
		| keyof DefaultSchema['CompositeTypes']
		| { schema: keyof Database },
	CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
		schema: keyof Database;
	}
		? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
		: never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
	? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
	: PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
		? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
		: never;

export const Constants = {
	graphql_public: {
		Enums: {},
	},
	public: {
		Enums: {
			checklist_item_status: ['Incomplete', 'Deferred', 'Complete'],
			entity_type: ['organization', 'client', 'project'],
			invite_resource_type: ['organization', 'client', 'project'],
			invite_status: ['pending', 'accepted', 'revoked', 'expired', 'declined'],
			membership_role: ['viewer', 'editor', 'admin', 'owner'],
			wbs_item_type: ['Standard', 'Custom'],
		},
	},
} as const;
